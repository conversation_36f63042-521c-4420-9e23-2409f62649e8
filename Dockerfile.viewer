# Node.js based Potree viewer with dynamic path handling
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install dependencies
COPY package.json package-lock.json* ./
RUN npm install

# Copy application files
COPY server.js ./
COPY public ./public
COPY viewer ./viewer

# Create data directory for mounted volumes
RUN mkdir -p /app/public/data

# Expose port
EXPOSE 3000

# Start the server
CMD ["node", "server.js"]
