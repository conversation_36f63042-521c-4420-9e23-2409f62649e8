version: '3.8'

services:
  potree-converter:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: potree-converter
    volumes:
      # Mount the las_laz folder for input files
      - ./las_laz:/app/las_laz
      # Mount output folder for converted files
      - ./output:/app/output
      # Optional: mount a config folder for custom settings
      - ./config:/app/config
    environment:
      - CONVERT_ALL=true
      - OUTPUT_FORMAT=potree
      - SPACING=auto
      - LEVELS=5
      - OUTPUT_ATTRIBUTES=RGB,INTENSITY,CLASSIFICATION
    working_dir: /app
    # Keep container running for interactive use
    stdin_open: true
    tty: true
    # Uncomment the line below if you want the container to run once and exit
    # command: /app/convert.sh

  # Web server with dynamic Potree viewer
  potree-viewer:
    build:
      context: .
      dockerfile: Dockerfile.viewer
    container_name: potree-viewer
    ports:
      - "8080:3000"
    volumes:
      - ./output:/app/public/data
      - ./viewer:/app/viewer
    depends_on:
      - potree-converter
    profiles:
      - viewer
