# Potree Converter Docker Setup

This Docker setup provides a containerized Potree converter for processing LAS/LAZ point cloud files into web-viewable Potree format.

## Features

- **Ubuntu 22.04** base image with all required dependencies
- **Volume mounting** for easy file input/output
- **Automatic conversion** of all LAS/LAZ files in the input folder
- **Optional web viewer** using Nginx to serve converted files
- **Configurable conversion parameters** via environment variables

## Directory Structure

```
potree/
├── Dockerfile              # Container definition
├── docker-compose.yml      # Service orchestration
├── convert.sh              # Conversion script
├── nginx.conf              # Web server configuration
├── las_laz/                # Input folder (mount your LAS/LAZ files here)
├── output/                 # Output folder (converted Potree files)
├── config/                 # Optional configuration files
└── README.md               # This file
```

## Quick Start

1. **Create the input directory and add your files:**
   ```bash
   mkdir -p las_laz
   # Copy your .las or .laz files to the las_laz folder
   cp /path/to/your/pointcloud.las las_laz/
   ```

2. **Build and run the converter:**
   ```bash
   docker-compose up --build
   ```

3. **View converted files:**
   The converted Potree files will be available in the `output/` folder.

## Usage Options

### Option 1: Automatic Conversion (Default)
```bash
# Build and run - converts all files automatically
docker-compose up --build
```

### Option 2: Interactive Mode
```bash
# Run in interactive mode
docker-compose run --rm potree-converter bash

# Inside the container, run manual conversions:
PotreeConverter /app/las_laz/P2_Pointcloud.las -o /app/output/P2_Pointcloud
```

### Option 3: With Web Viewer
```bash
# Start both converter and web viewer
docker-compose --profile viewer up -d --build

# Access the viewer at http://localhost:8080
```

## Configuration

You can customize the conversion process using environment variables in `docker-compose.yml`:

```yaml
environment:
  - CONVERT_ALL=true                    # Auto-convert all files
  - OUTPUT_FORMAT=potree               # Output format
  - SPACING=auto                       # Point spacing (auto or numeric value)
  - LEVELS=5                          # Number of LOD levels
  - OUTPUT_ATTRIBUTES=RGB,INTENSITY,CLASSIFICATION  # Attributes to include
```

## Manual Conversion Commands

If you prefer manual control, you can exec into the running container:

```bash
# Start container in background
docker-compose up -d

# Execute commands inside the container
docker exec -it potree-converter bash

# Run custom conversions
PotreeConverter /app/las_laz/input.las -o /app/output/custom_output --spacing 0.1 --levels 6
```

## Common PotreeConverter Options

- `--spacing <value>`: Point spacing for the output (e.g., 0.1, 0.01)
- `--levels <number>`: Number of levels in the octree (default: 5)
- `--output-attributes <list>`: Comma-separated list of attributes (RGB, INTENSITY, CLASSIFICATION, etc.)
- `--material <type>`: Material type (RGB, ELEVATION, INTENSITY, etc.)

## Troubleshooting

1. **No files found**: Make sure your LAS/LAZ files are in the `las_laz/` folder
2. **Permission issues**: Ensure the Docker daemon has access to your directories
3. **Memory issues**: Large point clouds may require more memory - adjust Docker settings
4. **Build failures**: Make sure you have sufficient disk space and internet connection

## File Formats Supported

- **Input**: LAS (.las), LAZ (.laz)
- **Output**: Potree format (octree structure with .bin files and metadata)

## Web Viewer

The optional web viewer serves the converted files through Nginx:
- Access at `http://localhost:8080`
- CORS enabled for cross-origin requests
- Optimized for serving Potree binary files

## Stopping the Services

```bash
# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v

# Stop and remove images
docker-compose down --rmi all
```
