# Potree Converter & Viewer Docker Setup

This Docker setup provides a complete solution for processing LAS/LAZ point cloud files and viewing them through a dynamic web interface powered by Express.js and Potree.

## Features

- **Ubuntu 22.04** base image with all required dependencies
- **Volume mounting** for easy file input/output
- **Automatic conversion** of all LAS/LAZ files in the input folder
- **Dynamic web viewer** with Express.js server supporting both Potree 1.x and 2.x formats
- **Auto-detection** of point cloud formats and versions
- **RESTful API** for point cloud metadata and listing
- **Configurable conversion parameters** via environment variables

## Directory Structure

```
potree/
├── Dockerfile              # Converter container definition
├── Dockerfile.viewer       # Viewer container definition
├── docker-compose.yml      # Service orchestration
├── convert.sh              # Conversion script
├── server.js               # Express.js web server
├── package.json            # Node.js dependencies
├── las_laz/                # Input folder (mount your LAS/LAZ files here)
├── output/                 # Output folder (converted Potree files)
├── viewer/                 # Potree viewer library
├── config/                 # Optional configuration files
└── README.md               # This file
```

## Quick Start

1. **Create the input directory and add your files:**
   ```bash
   mkdir -p las_laz
   # Copy your .las or .laz files to the las_laz folder
   cp /path/to/your/pointcloud.las las_laz/
   ```

2. **Build and run the complete system:**
   ```bash
   # Start converter and web viewer
   docker-compose --profile viewer up -d --build
   ```

3. **Access the web interface:**
   - **Home page:** `http://localhost:8080/` - Lists all available point clouds
   - **Point cloud viewer:** `http://localhost:8080/{pointcloud_name}/` - Interactive 3D viewer
   - **API endpoints:** `http://localhost:8080/api/pointclouds` - JSON list of point clouds

## Usage Options

### Option 1: Complete System (Recommended)
```bash
# Start converter and dynamic web viewer
docker-compose --profile viewer up -d --build

# Access the web interface:
# - Home: http://localhost:8080/
# - Point Cloud: http://localhost:8080/P2_Pointcloud/
# - API: http://localhost:8080/api/pointclouds
```

### Option 2: Converter Only
```bash
# Build and run converter only
docker-compose up --build potree-converter
```

### Option 3: Interactive Mode
```bash
# Run in interactive mode
docker-compose run --rm potree-converter bash

# Inside the container, run manual conversions:
PotreeConverter /app/las_laz/P2_Pointcloud.las -o /app/output/P2_Pointcloud
```

## Configuration

You can customize the conversion process using environment variables in `docker-compose.yml`:

```yaml
environment:
  - CONVERT_ALL=true                    # Auto-convert all files
  - OUTPUT_FORMAT=potree               # Output format
  - SPACING=auto                       # Point spacing (auto or numeric value)
  - LEVELS=5                          # Number of LOD levels
  - OUTPUT_ATTRIBUTES=RGB,INTENSITY,CLASSIFICATION  # Attributes to include
```

## Manual Conversion Commands

If you prefer manual control, you can exec into the running container:

```bash
# Start container in background
docker-compose up -d

# Execute commands inside the container
docker exec -it potree-converter bash

# Run custom conversions
PotreeConverter /app/las_laz/input.las -o /app/output/custom_output --spacing 0.1 --levels 6
```

## Common PotreeConverter Options

- `--spacing <value>`: Point spacing for the output (e.g., 0.1, 0.01)
- `--levels <number>`: Number of levels in the octree (default: 5)
- `--output-attributes <list>`: Comma-separated list of attributes (RGB, INTENSITY, CLASSIFICATION, etc.)
- `--material <type>`: Material type (RGB, ELEVATION, INTENSITY, etc.)

## Troubleshooting

1. **No files found**: Make sure your LAS/LAZ files are in the `las_laz/` folder
2. **Permission issues**: Ensure the Docker daemon has access to your directories
3. **Memory issues**: Large point clouds may require more memory - adjust Docker settings
4. **Build failures**: Make sure you have sufficient disk space and internet connection

## File Formats Supported

- **Input**: LAS (.las), LAZ (.laz)
- **Output**: Potree format (octree structure with .bin files and metadata)

## Web Viewer Features

The dynamic web viewer is powered by Express.js and provides:

### **Home Page** (`http://localhost:8080/`)
- Lists all available point clouds with version badges
- Auto-detects Potree 1.x (cloud.js) and 2.x (metadata.json) formats
- Shows validation status for each point cloud
- Direct links to individual viewers

### **Point Cloud Viewer** (`http://localhost:8080/{name}/`)
- Interactive 3D Potree viewer
- Supports both Potree 1.x and 2.x formats
- Auto-detection of point cloud version
- Full navigation and visualization controls
- Material and rendering options

### **API Endpoints**
- `GET /api/pointclouds` - JSON list of all point clouds
- `GET /api/pointcloud/{name}` - Metadata for specific point cloud
- `GET /data/{name}/` - Static file serving for point cloud data

### **Supported Formats**
- **Potree 1.x**: Uses `cloud.js` files
- **Potree 2.x**: Uses `metadata.json` files with binary data
- Auto-detection based on file presence and metadata version

## Examples

### Converting and Viewing a Point Cloud
```bash
# 1. Add your LAS file
cp mydata.las las_laz/

# 2. Start the system
docker-compose --profile viewer up -d --build

# 3. Wait for conversion to complete (check logs)
docker-compose logs -f potree-converter

# 4. Access the viewer
# Home: http://localhost:8080/
# Direct: http://localhost:8080/mydata/
```

### API Usage
```bash
# List all point clouds
curl http://localhost:8080/api/pointclouds

# Get specific point cloud metadata
curl http://localhost:8080/api/pointcloud/P2_Pointcloud
```

## Stopping the Services

```bash
# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v

# Stop and remove images
docker-compose down --rmi all
```

## Development

The Express.js server (`server.js`) provides:
- Dynamic routing for point clouds
- Auto-detection of Potree formats
- RESTful API for metadata
- Static file serving optimized for Potree
- Error handling and validation

To modify the viewer, edit `server.js` and restart the viewer container:
```bash
docker-compose restart potree-viewer
```
