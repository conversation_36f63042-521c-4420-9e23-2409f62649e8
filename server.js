const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 3000;

// Serve static files
app.use('/libs', express.static(path.join(__dirname, 'viewer/libs')));
app.use('/build', express.static(path.join(__dirname, 'viewer/build')));
app.use('/data', express.static(path.join(__dirname, 'public/data')));

// Function to check if a folder contains valid Potree data
function isValidPotreeFolder(folderPath) {
    const hasMetadata = fs.existsSync(path.join(folderPath, 'metadata.json'));

    // Check for Potree 1.x format (cloud.js)
    const hasCloudJs = fs.existsSync(path.join(folderPath, 'cloud.js'));

    // Check for Potree 2.x format (octree.bin or hierarchy.bin)
    const hasOctreeBin = fs.existsSync(path.join(folderPath, 'octree.bin'));
    const hasHierarchyBin = fs.existsSync(path.join(folderPath, 'hierarchy.bin'));

    // Valid if has metadata and either cloud.js (v1.x) or binary files (v2.x)
    return hasMetadata && (hasCloudJs || hasOctreeBin || hasHierarchyBin);
}

// Function to detect Potree version
function getPotreeVersion(folderPath) {
    const metadataPath = path.join(folderPath, 'metadata.json');
    if (fs.existsSync(metadataPath)) {
        try {
            const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
            return metadata.version || '1.0';
        } catch (error) {
            return '1.0';
        }
    }
    return '1.0';
}

// API endpoint to get available point clouds
app.get('/api/pointclouds', (req, res) => {
    const dataDir = path.join(__dirname, 'public/data');

    try {
        const folders = fs.readdirSync(dataDir, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => {
                const folderPath = path.join(dataDir, dirent.name);
                const valid = isValidPotreeFolder(folderPath);
                const version = valid ? getPotreeVersion(folderPath) : null;

                return {
                    name: dirent.name,
                    path: dirent.name,
                    valid: valid,
                    version: version,
                    url: `/data/${dirent.name}`
                };
            })
            .filter(item => item.valid);

        res.json(folders);
    } catch (error) {
        console.error('Error reading data directory:', error);
        res.status(500).json({ error: 'Unable to read point cloud data' });
    }
});

// API endpoint to get point cloud metadata
app.get('/api/pointcloud/:name', (req, res) => {
    const pointcloudName = req.params.name;
    const metadataPath = path.join(__dirname, 'public/data', pointcloudName, 'metadata.json');
    
    try {
        if (fs.existsSync(metadataPath)) {
            const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
            res.json({
                name: pointcloudName,
                path: pointcloudName,
                url: `/data/${pointcloudName}`,
                metadata: metadata
            });
        } else {
            res.status(404).json({ error: 'Point cloud not found' });
        }
    } catch (error) {
        console.error('Error reading metadata:', error);
        res.status(500).json({ error: 'Unable to read point cloud metadata' });
    }
});

// Dynamic route for point cloud viewer
app.get('/:pointcloudName', (req, res) => {
    const pointcloudName = req.params.pointcloudName;
    const pointcloudPath = path.join(__dirname, 'public/data', pointcloudName);

    // Check if point cloud exists
    if (!fs.existsSync(pointcloudPath)) {
        return res.status(404).send(`
            <html>
                <head><title>Point Cloud Not Found</title></head>
                <body>
                    <h1>Point Cloud "${pointcloudName}" Not Found</h1>
                    <p><a href="/">Back to Home</a></p>
                </body>
            </html>
        `);
    }

    // Check if it's a valid Potree point cloud
    if (!isValidPotreeFolder(pointcloudPath)) {
        return res.status(400).send(`
            <html>
                <head><title>Invalid Point Cloud</title></head>
                <body>
                    <h1>Invalid Point Cloud Format</h1>
                    <p>The folder "${pointcloudName}" does not contain valid Potree data.</p>
                    <p>Required files: metadata.json and either cloud.js (v1.x) or octree.bin/hierarchy.bin (v2.x)</p>
                    <p><a href="/">Back to Home</a></p>
                </body>
            </html>
        `);
    }

    // Get Potree version and serve appropriate viewer
    const version = getPotreeVersion(pointcloudPath);
    const viewerHtml = generateViewerHtml(pointcloudName, version);
    res.send(viewerHtml);
});

// Home page - list all available point clouds
app.get('/', (req, res) => {
    const dataDir = path.join(__dirname, 'public/data');
    let pointclouds = [];
    
    try {
        if (fs.existsSync(dataDir)) {
            pointclouds = fs.readdirSync(dataDir, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => {
                    const folderPath = path.join(dataDir, dirent.name);
                    const valid = isValidPotreeFolder(folderPath);
                    const version = valid ? getPotreeVersion(folderPath) : null;

                    return {
                        name: dirent.name,
                        valid: valid,
                        version: version
                    };
                });
        }
    } catch (error) {
        console.error('Error reading data directory:', error);
    }
    
    const homeHtml = generateHomeHtml(pointclouds);
    res.send(homeHtml);
});

function generateViewerHtml(pointcloudName, version = '1.0') {
    // For Potree 2.x, we need different loading approach
    const isVersion2 = version.startsWith('2.');

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Potree Viewer - ${pointcloudName}</title>

    <link rel="stylesheet" type="text/css" href="/build/potree/potree.css">
    <link rel="stylesheet" type="text/css" href="/libs/jquery-ui/jquery-ui.min.css">
    <link rel="stylesheet" type="text/css" href="/libs/openlayers3/ol.css">
    <link rel="stylesheet" type="text/css" href="/libs/spectrum/spectrum.css">
    <link rel="stylesheet" type="text/css" href="/libs/jstree/themes/mixed/style.css">
</head>

<body>
    <script src="/libs/jquery/jquery-3.1.1.min.js"></script>
    <script src="/libs/spectrum/spectrum.js"></script>
    <script src="/libs/jquery-ui/jquery-ui.min.js"></script>
    <script src="/libs/other/BinaryHeap.js"></script>
    <script src="/libs/tween/tween.min.js"></script>
    <script src="/libs/d3/d3.js"></script>
    <script src="/libs/proj4/proj4.js"></script>
    <script src="/libs/openlayers3/ol.js"></script>
    <script src="/libs/i18next/i18next.js"></script>
    <script src="/libs/jstree/jstree.js"></script>
    <script src="/build/potree/potree.js"></script>

    <div class="potree_container" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; ">
        <div id="potree_render_area" style="background-image: url('/build/potree/resources/images/background_gradient.jpg');"></div>
        <div id="potree_sidebar_container"> </div>
    </div>

    <script>
        window.viewer = new Potree.Viewer(document.getElementById("potree_render_area"));
        
        viewer.setEDLEnabled(true);
        viewer.setFOV(60);
        viewer.setPointBudget(1*1000*1000);
        viewer.loadSettingsFromURL();
        
        viewer.setDescription("Point Cloud: ${pointcloudName}");
        
        viewer.loadGUI(() => {
            viewer.setLanguage('en');
            $("#menu_appearance").next().show();
            $("#menu_tools").next().show();
            $("#menu_scene").next().show();
            viewer.toggleSidebar();
        });

        // Load the point cloud - support both v1.x and v2.x formats
        ${isVersion2 ? `
        // Potree 2.x format - load from metadata.json
        Potree.loadPointCloud("/data/${pointcloudName}/metadata.json", "${pointcloudName}", e => {
            let scene = viewer.scene;
            let pointcloud = e.pointcloud;

            let material = pointcloud.material;
            material.size = 1;
            material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
            material.shape = Potree.PointShape.SQUARE;

            scene.addPointCloud(pointcloud);
            viewer.fitToScreen();
        });
        ` : `
        // Potree 1.x format - load from cloud.js
        Potree.loadPointCloud("/data/${pointcloudName}/cloud.js", "${pointcloudName}", e => {
            let scene = viewer.scene;
            let pointcloud = e.pointcloud;

            let material = pointcloud.material;
            material.size = 1;
            material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
            material.shape = Potree.PointShape.SQUARE;

            scene.addPointCloud(pointcloud);
            viewer.fitToScreen();
        });
        `}
    </script>
</body>
</html>`;
}

function generateHomeHtml(pointclouds) {
    const pointcloudList = pointclouds.map(pc => {
        if (pc.valid) {
            const versionBadge = pc.version ? `<span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px; margin-left: 8px;">v${pc.version}</span>` : '';
            return `<li><a href="/${pc.name}" style="color: #007bff; text-decoration: none;">${pc.name}</a> ✅${versionBadge}</li>`;
        } else {
            return `<li>${pc.name} ❌ (Invalid Potree format)</li>`;
        }
    }).join('');
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Potree Viewer - Point Cloud Library</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
        ul { list-style-type: none; padding: 0; }
        li { padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }
        a { color: #007bff; text-decoration: none; font-weight: bold; }
        a:hover { text-decoration: underline; }
        .empty { text-align: center; color: #666; font-style: italic; }
        .footer { margin-top: 30px; text-align: center; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Potree Viewer - Point Cloud Library</h1>
        
        <div class="info">
            <strong>📍 Current URL Pattern:</strong> <code>http://localhost:8080/{pointcloud_name}</code><br>
            <strong>📁 Data Directory:</strong> <code>/app/public/data/</code>
        </div>
        
        <h2>📊 Available Point Clouds:</h2>
        
        ${pointclouds.length > 0 ? `<ul>${pointcloudList}</ul>` : '<div class="empty">No point clouds found. Please convert some LAS/LAZ files first.</div>'}
        
        <div class="footer">
            <p>💡 To add new point clouds, convert LAS/LAZ files using the Potree converter.</p>
            <p>🔄 Refresh this page after adding new point clouds.</p>
        </div>
    </div>
</body>
</html>`;
}

app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 Potree Viewer Server running on http://localhost:${PORT}`);
    console.log(`📁 Serving point clouds from: /app/public/data/`);
    console.log(`🌐 URL pattern: http://localhost:${PORT}/{pointcloud_name}`);
});
