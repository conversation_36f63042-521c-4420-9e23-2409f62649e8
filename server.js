const express = require('express');
const path = require('path');
const fs = require('fs');
const session = require('express-session');

const app = express();
const PORT = 3000;

// Admin credentials
const ADMIN_USER = 'admin';
const ADMIN_PASS = '2321';

// Session configuration
app.use(session({
    secret: 'potree-admin-secret-key-2024',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // Set to true if using HTTPS
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
}));

// Parse form data
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// Authentication middleware
function requireAuth(req, res, next) {
    if (req.session && req.session.authenticated) {
        return next();
    } else {
        return res.redirect('/login');
    }
}

// Login routes
app.get('/login', (req, res) => {
    if (req.session && req.session.authenticated) {
        return res.redirect('/');
    }

    res.send(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Potree Admin Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 400px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, input[type="password"]:focus {
            border-color: #667eea;
            outline: none;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #5a6fd8;
        }
        .error {
            color: #e74c3c;
            text-align: center;
            margin-top: 15px;
        }
        .logo {
            text-align: center;
            margin-bottom: 20px;
            font-size: 48px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🌐</div>
        <h1>Potree Admin Panel</h1>
        <form method="POST" action="/login">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit">Login</button>
            ${req.query.error ? '<div class="error">Invalid username or password</div>' : ''}
        </form>
    </div>
</body>
</html>`);
});

app.post('/login', (req, res) => {
    const { username, password } = req.body;

    if (username === ADMIN_USER && password === ADMIN_PASS) {
        req.session.authenticated = true;
        req.session.username = username;
        res.redirect('/');
    } else {
        res.redirect('/login?error=1');
    }
});

app.get('/logout', (req, res) => {
    req.session.destroy((err) => {
        if (err) {
            console.error('Session destruction error:', err);
        }
        res.redirect('/login');
    });
});

// Serve static files
app.use('/libs', express.static(path.join(__dirname, 'viewer/libs')));
app.use('/build', express.static(path.join(__dirname, 'viewer/build')));
app.use('/data', express.static(path.join(__dirname, 'public/data')));

// Function to check if a folder contains valid Potree data
function isValidPotreeFolder(folderPath) {
    const hasMetadata = fs.existsSync(path.join(folderPath, 'metadata.json'));

    // Check for Potree 1.x format (cloud.js)
    const hasCloudJs = fs.existsSync(path.join(folderPath, 'cloud.js'));

    // Check for Potree 2.x format (octree.bin or hierarchy.bin)
    const hasOctreeBin = fs.existsSync(path.join(folderPath, 'octree.bin'));
    const hasHierarchyBin = fs.existsSync(path.join(folderPath, 'hierarchy.bin'));

    // Valid if has metadata and either cloud.js (v1.x) or binary files (v2.x)
    return hasMetadata && (hasCloudJs || hasOctreeBin || hasHierarchyBin);
}

// Function to detect Potree version
function getPotreeVersion(folderPath) {
    const metadataPath = path.join(folderPath, 'metadata.json');
    if (fs.existsSync(metadataPath)) {
        try {
            const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
            return metadata.version || '1.0';
        } catch (error) {
            return '1.0';
        }
    }
    return '1.0';
}

// API endpoint to get available point clouds (protected)
app.get('/api/pointclouds', requireAuth, (req, res) => {
    const dataDir = path.join(__dirname, 'public/data');

    try {
        const folders = fs.readdirSync(dataDir, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => {
                const folderPath = path.join(dataDir, dirent.name);
                const valid = isValidPotreeFolder(folderPath);
                const version = valid ? getPotreeVersion(folderPath) : null;

                // Get folder creation/modification time
                const stats = fs.statSync(folderPath);
                const createdDate = stats.birthtime || stats.mtime;

                return {
                    name: dirent.name,
                    path: dirent.name,
                    valid: valid,
                    version: version,
                    url: `/data/${dirent.name}`,
                    createdDate: createdDate,
                    createdDateString: createdDate.toLocaleString('th-TH', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    })
                };
            })
            .filter(item => item.valid)
            .sort((a, b) => b.createdDate - a.createdDate); // Sort by date: newest first

        res.json(folders);
    } catch (error) {
        console.error('Error reading data directory:', error);
        res.status(500).json({ error: 'Unable to read point cloud data' });
    }
});

// API endpoint to get point cloud metadata (protected)
app.get('/api/pointcloud/:name', requireAuth, (req, res) => {
    const pointcloudName = req.params.name;
    const metadataPath = path.join(__dirname, 'public/data', pointcloudName, 'metadata.json');

    try {
        if (fs.existsSync(metadataPath)) {
            const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
            res.json({
                name: pointcloudName,
                path: pointcloudName,
                url: `/data/${pointcloudName}`,
                metadata: metadata
            });
        } else {
            res.status(404).json({ error: 'Point cloud not found' });
        }
    } catch (error) {
        console.error('Error reading metadata:', error);
        res.status(500).json({ error: 'Unable to read point cloud metadata' });
    }
});

// Dynamic route for point cloud viewer (protected)
app.get('/:pointcloudName', requireAuth, (req, res) => {
    const pointcloudName = req.params.pointcloudName;
    const pointcloudPath = path.join(__dirname, 'public/data', pointcloudName);

    // Check if point cloud exists
    if (!fs.existsSync(pointcloudPath)) {
        return res.status(404).send(`
            <html>
                <head><title>Point Cloud Not Found</title></head>
                <body>
                    <h1>Point Cloud "${pointcloudName}" Not Found</h1>
                    <p><a href="/">Back to Home</a></p>
                </body>
            </html>
        `);
    }

    // Check if it's a valid Potree point cloud
    if (!isValidPotreeFolder(pointcloudPath)) {
        return res.status(400).send(`
            <html>
                <head><title>Invalid Point Cloud</title></head>
                <body>
                    <h1>Invalid Point Cloud Format</h1>
                    <p>The folder "${pointcloudName}" does not contain valid Potree data.</p>
                    <p>Required files: metadata.json and either cloud.js (v1.x) or octree.bin/hierarchy.bin (v2.x)</p>
                    <p><a href="/">Back to Home</a></p>
                </body>
            </html>
        `);
    }

    // Get Potree version and serve appropriate viewer
    const version = getPotreeVersion(pointcloudPath);
    const viewerHtml = generateViewerHtml(pointcloudName, version);
    res.send(viewerHtml);
});

// Home page - list all available point clouds (protected)
app.get('/', requireAuth, (req, res) => {
    const dataDir = path.join(__dirname, 'public/data');
    let pointclouds = [];

    try {
        if (fs.existsSync(dataDir)) {
            pointclouds = fs.readdirSync(dataDir, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => {
                    const folderPath = path.join(dataDir, dirent.name);
                    const valid = isValidPotreeFolder(folderPath);
                    const version = valid ? getPotreeVersion(folderPath) : null;

                    // Get folder creation/modification time
                    const stats = fs.statSync(folderPath);
                    const createdDate = stats.birthtime || stats.mtime;

                    return {
                        name: dirent.name,
                        valid: valid,
                        version: version,
                        createdDate: createdDate,
                        createdDateString: createdDate.toLocaleString('th-TH', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                        })
                    };
                })
                .sort((a, b) => b.createdDate - a.createdDate); // Sort by date: newest first
        }
    } catch (error) {
        console.error('Error reading data directory:', error);
    }

    const homeHtml = generateHomeHtml(pointclouds, req.session.username);
    res.send(homeHtml);
});

function generateViewerHtml(pointcloudName, version = '1.0') {
    // For Potree 2.x, we need different loading approach
    const isVersion2 = version.startsWith('2.');

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Potree Viewer - ${pointcloudName}</title>

    <link rel="stylesheet" type="text/css" href="/build/potree/potree.css">
    <link rel="stylesheet" type="text/css" href="/libs/jquery-ui/jquery-ui.min.css">
    <link rel="stylesheet" type="text/css" href="/libs/openlayers3/ol.css">
    <link rel="stylesheet" type="text/css" href="/libs/spectrum/spectrum.css">
    <link rel="stylesheet" type="text/css" href="/libs/jstree/themes/mixed/style.css">
</head>

<body>
    <script src="/libs/jquery/jquery-3.1.1.min.js"></script>
    <script src="/libs/spectrum/spectrum.js"></script>
    <script src="/libs/jquery-ui/jquery-ui.min.js"></script>
    <script src="/libs/other/BinaryHeap.js"></script>
    <script src="/libs/tween/tween.min.js"></script>
    <script src="/libs/d3/d3.js"></script>
    <script src="/libs/proj4/proj4.js"></script>
    <script src="/libs/openlayers3/ol.js"></script>
    <script src="/libs/i18next/i18next.js"></script>
    <script src="/libs/jstree/jstree.js"></script>
    <script src="/build/potree/potree.js"></script>

    <div class="potree_container" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; ">
        <div id="potree_render_area" style="background-image: url('/build/potree/resources/images/background_gradient.jpg');"></div>
        <div id="potree_sidebar_container"> </div>
    </div>

    <script>
        window.viewer = new Potree.Viewer(document.getElementById("potree_render_area"));
        
        viewer.setEDLEnabled(true);
        viewer.setFOV(60);
        viewer.setPointBudget(1*1000*1000);
        viewer.loadSettingsFromURL();
        
        viewer.setDescription("Point Cloud: ${pointcloudName}");
        
        viewer.loadGUI(() => {
            viewer.setLanguage('en');
            $("#menu_appearance").next().show();
            $("#menu_tools").next().show();
            $("#menu_scene").next().show();
            viewer.toggleSidebar();
        });

        // Load the point cloud - support both v1.x and v2.x formats
        ${isVersion2 ? `
        // Potree 2.x format - load from metadata.json
        Potree.loadPointCloud("/data/${pointcloudName}/metadata.json", "${pointcloudName}", e => {
            let scene = viewer.scene;
            let pointcloud = e.pointcloud;

            let material = pointcloud.material;
            material.size = 1;
            material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
            material.shape = Potree.PointShape.SQUARE;

            scene.addPointCloud(pointcloud);
            viewer.fitToScreen();
        });
        ` : `
        // Potree 1.x format - load from cloud.js
        Potree.loadPointCloud("/data/${pointcloudName}/cloud.js", "${pointcloudName}", e => {
            let scene = viewer.scene;
            let pointcloud = e.pointcloud;

            let material = pointcloud.material;
            material.size = 1;
            material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
            material.shape = Potree.PointShape.SQUARE;

            scene.addPointCloud(pointcloud);
            viewer.fitToScreen();
        });
        `}
    </script>
</body>
</html>`;
}

function generateHomeHtml(pointclouds, username) {
    const pointcloudList = pointclouds.map(pc => {
        if (pc.valid) {
            const versionBadge = pc.version ? `<span style="background: #28a745; color: white; padding: 3px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">v${pc.version}</span>` : '';
            const dateBadge = pc.createdDateString ? `<span style="background: #17a2b8; color: white; padding: 3px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">� ${pc.createdDateString}</span>` : '';

            return `<li>
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 5px 0;">
                    <div style="flex: 1;">
                        <div style="margin-bottom: 5px;">
                            <a href="/${pc.name}" style="color: #007bff; text-decoration: none; font-weight: bold; font-size: 16px;">${pc.name}</a>
                            <span style="color: #28a745; margin-left: 8px; font-size: 14px;">✅</span>
                        </div>
                        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                            ${versionBadge}
                            ${dateBadge}
                        </div>
                    </div>
                    <div style="text-align: right; color: #6c757d; font-size: 12px;">
                        <div>📊 Point Cloud</div>
                        <div>🔗 Ready to view</div>
                    </div>
                </div>
            </li>`;
        } else {
            return `<li>
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 5px 0;">
                    <div>
                        <span style="color: #6c757d; font-weight: bold;">${pc.name}</span>
                        <span style="color: #dc3545; margin-left: 8px;">❌ Invalid Potree format</span>
                    </div>
                </div>
            </li>`;
        }
    }).join('');
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Potree Admin - Point Cloud Library</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin: 0; }
        .admin-info { display: flex; align-items: center; gap: 15px; }
        .admin-badge { background: #28a745; color: white; padding: 5px 12px; border-radius: 15px; font-size: 12px; font-weight: bold; }
        .logout-btn { background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 5px; text-decoration: none; font-size: 14px; cursor: pointer; }
        .logout-btn:hover { background: #c82333; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .stats { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745; }
        .sort-info { background: #e3f2fd; padding: 12px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #2196f3; }
        ul { list-style-type: none; padding: 0; }
        li { padding: 18px; margin: 10px 0; background: #ffffff; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05); transition: all 0.2s ease; }
        li:hover { box-shadow: 0 4px 8px rgba(0,0,0,0.1); transform: translateY(-1px); }
        a { color: #007bff; text-decoration: none; font-weight: bold; }
        a:hover { text-decoration: underline; }
        .empty { text-align: center; color: #666; font-style: italic; padding: 40px; }
        .footer { margin-top: 30px; text-align: center; color: #666; font-size: 14px; }
        .point-cloud-item { display: flex; justify-content: space-between; align-items: center; }
        .point-cloud-info { flex: 1; }
        .badges { display: flex; gap: 5px; flex-wrap: wrap; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 Potree Admin Panel</h1>
            <div class="admin-info">
                <span class="admin-badge">👤 ${username || 'Admin'}</span>
                <a href="/logout" class="logout-btn">🚪 Logout</a>
            </div>
        </div>

        <div class="stats">
            <strong>📊 Statistics:</strong>
            ${pointclouds.length} point cloud(s) available |
            ${pointclouds.filter(pc => pc.valid).length} valid |
            ${pointclouds.filter(pc => !pc.valid).length} invalid
        </div>

        <div class="info">
            <strong>📍 URL Pattern:</strong> <code>http://localhost:8080/{pointcloud_name}</code><br>
            <strong>📁 Data Directory:</strong> <code>/app/public/data/</code>
        </div>

        <div class="sort-info">
            <strong>🔄 Sorting Order:</strong> Point clouds are sorted by creation date (newest first)<br>
            <strong>🕒 Date Format:</strong> DD/MM/YYYY HH:MM (Thailand timezone)<br>
            <strong>📊 Display:</strong> Each point cloud shows version and creation timestamp
        </div>

        <h2>📊 Available Point Clouds:</h2>

        ${pointclouds.length > 0 ? `<ul>${pointcloudList}</ul>` : '<div class="empty">No point clouds found. Please convert some LAS/LAZ files first.</div>'}

        <div class="footer">
            <p>💡 To add new point clouds, convert LAS/LAZ files using the Potree converter.</p>
            <p>🔄 Refresh this page after adding new point clouds.</p>
            <p>🔒 This is a protected admin area.</p>
        </div>
    </div>
</body>
</html>`;
}

app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 Potree Viewer Server running on http://localhost:${PORT}`);
    console.log(`📁 Serving point clouds from: /app/public/data/`);
    console.log(`🌐 URL pattern: http://localhost:${PORT}/{pointcloud_name}`);
});
